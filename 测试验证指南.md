# 社区功能用户认证修复 - 测试验证指南

## 测试前准备

### 1. 清理测试环境
```swift
// 如果需要重置数据，可以在AppEntry.swift中临时启用以下代码：
// EAApp.forceDeleteAllSwiftDataFiles()
```

### 2. 确认修复文件
确认以下文件已更新：
- ✅ `Evolve/Core/Services/EASessionManager.swift`
- ✅ `Evolve/Features/Community/EAPostDetailViewModel.swift`
- ✅ `Evolve/AppEntry.swift`

## 核心测试场景

### 测试1：应用冷启动用户认证恢复

**测试步骤：**
1. 确保用户已登录
2. 完全关闭应用（从后台移除）
3. 重新启动应用
4. 进入社区页面
5. 尝试发表评论

**预期结果：**
- ✅ 应用启动后用户状态正确恢复
- ✅ 社区页面显示正常
- ✅ 评论功能正常工作，不提示"用户未登录"

**调试信息查看：**
```
在Xcode控制台查看以下日志：
🔍 [SessionManager] 恢复会话检查
✅ [SessionManager] 会话恢复成功
🚀 [AppEntry] 应用数据初始化完成
```

### 测试2：删除帖子权限检查

**测试步骤：**
1. 登录用户A
2. 发布一条帖子
3. 进入帖子详情页
4. 点击右上角"..."菜单
5. 检查是否显示"删除"选项
6. 尝试删除帖子

**预期结果：**
- ✅ 自己的帖子显示删除选项
- ✅ 删除功能正常工作
- ✅ 不再提示"用户未登录"

**调试信息查看：**
```
📝 [PostDetail] 开始删除帖子检查
✅ [PostDetail] 帖子删除成功
```

### 测试3：评论提交功能

**测试步骤：**
1. 进入任意帖子详情页
2. 在评论输入框输入内容
3. 点击发送按钮
4. 观察评论是否成功提交

**预期结果：**
- ✅ 评论成功提交
- ✅ 评论立即显示在列表中
- ✅ 不出现认证相关错误

**调试信息查看：**
```
📝 [PostDetail] 开始提交评论
✅ [PostDetail] 评论提交成功
```

### 测试4：内存压力下的会话保持

**测试步骤：**
1. 登录应用
2. 打开多个其他应用（消耗内存）
3. 返回Evolve应用
4. 尝试使用社区功能

**预期结果：**
- ✅ 用户状态保持正常
- ✅ 社区功能正常工作

### 测试5：网络异常处理

**测试步骤：**
1. 断开网络连接
2. 尝试发表评论或删除帖子
3. 观察错误提示

**预期结果：**
- ✅ 显示友好的网络错误提示
- ✅ 不显示技术性错误信息

## 错误场景测试

### 测试6：权限不足场景

**测试步骤：**
1. 登录用户A
2. 查看用户B发布的帖子
3. 检查是否显示删除选项

**预期结果：**
- ✅ 不显示删除选项（canDeletePost返回false）
- ✅ 如果强制尝试删除，显示"只能删除自己的帖子"

### 测试7：数据异常恢复

**测试步骤：**
1. 模拟数据异常（可通过调试器）
2. 观察应用的自动恢复机制

**预期结果：**
- ✅ 应用自动尝试恢复用户状态
- ✅ 显示适当的错误提示
- ✅ 不崩溃

## 性能测试

### 测试8：启动性能

**测试指标：**
- 应用启动时间
- 会话恢复时间
- UI响应速度

**预期结果：**
- ✅ 启动时间不明显增加
- ✅ 会话恢复在1秒内完成
- ✅ UI响应流畅

## 调试技巧

### 1. 查看详细日志
在Xcode控制台中过滤以下关键词：
- `[SessionManager]`
- `[PostDetail]`
- `[AppEntry]`

### 2. 断点调试
在以下方法设置断点：
- `EASessionManager.restoreSessionOnAppLaunch()`
- `EAPostDetailViewModel.getCurrentUser()`
- `EAPostDetailViewModel.deletePost()`

### 3. 状态检查
使用以下代码检查会话状态：
```swift
#if DEBUG
sessionManager.printSessionInfo()
#endif
```

## 常见问题排查

### 问题1：仍然提示"用户未登录"
**排查步骤：**
1. 检查UserDefaults中的用户数据
2. 验证Repository容器是否正确初始化
3. 查看会话恢复日志

### 问题2：删除功能不可用
**排查步骤：**
1. 检查`canDeletePost`的返回值
2. 验证帖子作者信息
3. 确认用户权限

### 问题3：应用启动慢
**排查步骤：**
1. 检查是否有重复的初始化调用
2. 优化会话恢复逻辑
3. 考虑异步加载

## 测试完成标准

所有测试通过的标准：
- ✅ 用户认证状态在应用重启后正确恢复
- ✅ 社区功能（评论、删除）正常工作
- ✅ 错误提示友好且准确
- ✅ 性能表现良好
- ✅ 无崩溃或异常行为

## 回归测试

修复完成后，建议进行以下回归测试：
1. 用户注册和登录流程
2. 其他页面的用户状态检查
3. Pro功能的权限验证
4. 数据同步功能

通过以上测试，可以确保修复的有效性和系统的稳定性。
