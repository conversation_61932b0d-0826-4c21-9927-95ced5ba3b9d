import Foundation
import SwiftData

/// 🔑 新增：社区ViewModel错误类型
enum EACommunityViewModelError: LocalizedError {
    case repositoryNotAvailable
    case userNotLoggedIn
    case postNotFound
    case operationFailed(String)

    var errorDescription: String? {
        switch self {
        case .repositoryNotAvailable:
            return "数据访问服务不可用"
        case .userNotLoggedIn:
            return "用户未登录"
        case .postNotFound:
            return "帖子不存在"
        case .operationFailed(let message):
            return "操作失败：\(message)"
        }
    }
}
import SwiftUI
import Combine

/// 社区页面ViewModel - 管理社区帖子列表和交互逻辑
/// 严格遵循项目MVVM架构规范，使用@MainActor确保UI更新在主线程
@MainActor
final class EACommunityViewModel: ObservableObject {
    
    // MARK: - 发布状态属性
    
    /// 帖子列表
    @Published var posts: [EACommunityPost] = []
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    /// 是否还有下一页
    @Published var hasNextPage: Bool = true
    
    /// 是否正在刷新（区别于普通加载）
    @Published var isRefreshing: Bool = false
    
    /// 页面大小
    private let pageSize: Int = 20
    
    /// 当前页码
    private var currentPage: Int = 0
    
    // MARK: - 依赖注入
    
    /// Repository容器（依赖注入，遵循Repository模式）
    private var repositoryContainer: EARepositoryContainer?
    
    // MARK: - 私有属性（性能优化）
    
    /// 上次刷新时间，用于防抖
    private var lastRefreshTime: Date = Date.distantPast
    
    /// 搜索防抖Timer
    private var searchDebounceTimer: Timer?
    
    // MARK: - 初始化
    
    /// 依赖注入构造器
    init() {
        // 空初始化，等待setRepositoryContainer调用
    }
    
    /// 设置Repository容器
    func setRepositoryContainer(_ container: EARepositoryContainer) {
        self.repositoryContainer = container
    }
    
    // MARK: - 公共方法
    
    /// 加载帖子列表
    func loadPosts() {
        guard !isLoading else { return }
        
        Task {
            await performLoadPosts()
        }
    }
    
    /// 刷新帖子列表
    func refreshPosts() {
        // 🚀 性能优化：防抖机制，避免频繁刷新
        let now = Date()
        if now.timeIntervalSince(lastRefreshTime) < 0.5 {
            return // 0.5秒内只允许一次刷新，提升用户体验
        }
        lastRefreshTime = now
        
        // 🔑 关键修复：不立即清空数据，在后台获取新数据后再替换
        currentPage = 0
        hasNextPage = true
        isRefreshing = true
        
        Task {
            await performRefreshPosts()
        }
    }
    
    /// 创建新帖子
    /// - Parameters:
    ///   - title: 帖子标题（暂时不使用，保留接口兼容性）
    ///   - content: 帖子内容
    ///   - images: 图片数据数组
    ///   - habitId: 关联的习惯ID（可选）
    func createPost(title: String, content: String, images: [Data] = [], habitId: UUID? = nil) async {
        guard let container = repositoryContainer else {
            errorMessage = "系统初始化未完成"
            return
        }

        isLoading = true
        defer { isLoading = false }

        do {
            // 🔑 修复：添加完善的错误处理
            guard let currentUser = try await container.getCurrentUser() else {
                errorMessage = "用户未登录，请先登录"
                return
            }

            // 验证内容不为空
            let trimmedContent = content.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedContent.isEmpty else {
                errorMessage = "帖子内容不能为空"
                return
            }

            // 🔑 新增：处理图片上传
            var imageURLs: [String] = []
            if !images.isEmpty {
                let imageProcessor = EAImageProcessor()
                for imageData in images {
                    let imagePath = try await imageProcessor.processAndSaveImage(imageData)
                    imageURLs.append(imagePath)
                }
            }

            // 创建帖子对象（使用正确的初始化方法）
            let post = EACommunityPost(
                content: trimmedContent,
                habitName: nil, // 暂时不关联习惯名称
                category: "general",
                energyLevel: 5
            )

            // 🔑 新增：设置图片URL
            post.imageURLs = imageURLs

            // 🔑 修复：移除ViewModel层的关系设置，让Repository层统一处理
            // 移除：post.author = currentUser

            // 通过Repository创建帖子
            let createdPost = try await container.communityRepository.createPost(post, authorId: currentUser.id)

            // 添加到本地列表顶部
            posts.insert(createdPost, at: 0)

            // 清空错误消息
            errorMessage = nil
            
        } catch {
            // 🔑 修复：详细的错误处理
            if let repoError = error as? EACommunityRepositoryError {
                switch repoError {
                case .userNotFound:
                    errorMessage = "用户会话已失效，请重新登录"
                case .dataCreateFailed:
                    errorMessage = "创建帖子失败，请检查网络连接"
                case .dataSaveFailed:
                    errorMessage = "保存帖子失败，请稍后重试"
                default:
                    errorMessage = "创建帖子失败：\(repoError.localizedDescription)"
                }
            } else {
                errorMessage = "创建帖子失败：\(error.localizedDescription)"
            }
        }
    }
    
    /// 🔑 新增：获取用户对特定帖子的点赞状态
    /// - Parameter post: 目标帖子
    /// - Returns: 是否已点赞
    func getUserLikeStatus(for post: EACommunityPost) async -> Bool {
        guard let container = repositoryContainer else { return false }

        do {
            guard let currentUser = try await container.getCurrentUser() else {
                return false
            }

            let likes = try await container.communityRepository.fetchLikes(for: post.id)
            return likes.contains { like in
                like.user?.id == currentUser.id && like.isActive
            }
        } catch {
            return false
        }
    }

    /// 切换帖子点赞状态
    /// - Parameter post: 要点赞/取消点赞的帖子
    func toggleLike(for post: EACommunityPost) async throws {
        guard let container = repositoryContainer else {
            throw EACommunityViewModelError.repositoryNotAvailable
        }

        guard let currentUser = try await container.getCurrentUser() else {
            throw EACommunityViewModelError.userNotLoggedIn
        }

        // ✅ 新增：Context一致性验证
        #if DEBUG
        print("🔍 ViewModel toggleLike - 使用Repository Container")
        #endif

        // 🔑 修复：Repository层已经通过syncLikeCount()更新了likeCount，无需手动更新
        let _ = try await container.communityRepository.toggleLike(
            postId: post.id,
            userId: currentUser.id
        )

        // 🔑 修复：重新获取更新后的帖子数据，确保UI显示正确的数量
        if let index = posts.firstIndex(where: { $0.id == post.id }) {
            if let updatedPost = try await container.communityRepository.fetchPost(by: post.id) {
                posts[index] = updatedPost
            }
        }
    }
    
    /// 搜索帖子
    /// - Parameter searchText: 搜索关键词
    func searchPosts(with searchText: String) {
        // 取消之前的搜索
        searchDebounceTimer?.invalidate()
        
        // 防抖处理：延迟0.5秒执行搜索
        searchDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
            Task { @MainActor in
                await self.performSearchPosts(searchText: searchText)
            }
        }
    }
    
    // MARK: - 删除帖子功能
    
    /// 删除帖子
    /// - Parameter post: 要删除的帖子
    func deletePost(_ post: EACommunityPost) async {
        guard let container = repositoryContainer else { return }
        
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 🔑 修复：使用增强版用户获取方法
            guard let currentUser = try await getEnhancedCurrentUser(container: container) else {
                errorMessage = "登录状态已过期，请重新登录"
                return
            }

            // 检查权限（只能删除自己的帖子）
            guard let postAuthor = post.getAuthor() else {
                errorMessage = "无法确定帖子作者"
                return
            }

            guard postAuthor.id == currentUser.id else {
                errorMessage = "只能删除自己发布的帖子"
                return
            }

            try await container.communityRepository.deletePost(id: post.id)

            // 从本地列表中移除
            posts.removeAll { $0.id == post.id }

            #if DEBUG
            print("✅ [Community] 帖子删除成功: \(post.id)")
            #endif

        } catch let error as NSError {
            // 🔑 改进：根据错误类型提供友好提示
            if error.domain == "CommunityError" && error.code == 1 {
                errorMessage = "登录状态已过期，请重新登录"
            } else if error.localizedDescription.contains("权限") {
                errorMessage = "权限不足，无法删除此帖子"
            } else if error.localizedDescription.contains("网络") {
                errorMessage = "网络连接异常，请检查网络后重试"
            } else {
                errorMessage = "删除失败，请稍后重试"
            }

            #if DEBUG
            print("❌ [Community] 删除失败: \(error.localizedDescription)")
            #endif
        }
    }
    
    /// 检查用户是否有删除权限（增强版）
    /// - Parameter post: 要检查的帖子
    /// - Returns: 是否有删除权限
    func canDeletePost(_ post: EACommunityPost) async -> Bool {
        guard let container = repositoryContainer else { return false }

        do {
            // 🔑 修复：使用增强版用户获取方法
            guard let currentUser = try await getEnhancedCurrentUser(container: container) else {
                return false
            }

            guard let postAuthor = post.getAuthor() else {
                return false
            }

            return postAuthor.id == currentUser.id
        } catch {
            #if DEBUG
            print("⚠️ [Community] 权限检查失败: \(error.localizedDescription)")
            #endif
            return false
        }
    }
    
    // MARK: - 评论相关功能
    
    /// 获取指定帖子的评论数量
    /// - Parameter post: 目标帖子
    /// - Returns: 评论数量
    func getCommentCount(for post: EACommunityPost) -> Int {
        // TODO: 通过Repository获取评论数量
        return 0 // 临时返回0，需要实现Repository方法
    }
    
    /// 添加评论
    /// - Parameters:
    ///   - post: 要评论的帖子
    ///   - content: 评论内容
    func addComment(to post: EACommunityPost, content: String) async {
        guard let container = repositoryContainer else { return }
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        do {
            // 🔑 修复：使用增强版用户获取方法
            guard let currentUser = try await getEnhancedCurrentUser(container: container) else {
                errorMessage = "登录状态已过期，请重新登录"
                return
            }

            // 创建评论对象（使用正确的初始化方法）
            let comment = EACommunityComment(
                content: content.trimmingCharacters(in: .whitespacesAndNewlines)
            )

            // 设置作者和帖子关系
            comment.author = currentUser
            comment.post = post

            let _ = try await container.communityRepository.createComment(comment, for: post.id, authorId: currentUser.id)

            // 更新本地帖子的评论数
            if let index = posts.firstIndex(where: { $0.id == post.id }) {
                posts[index].commentCount += 1
            }

            #if DEBUG
            print("✅ [Community] 评论添加成功")
            #endif

        } catch let error as NSError {
            // 🔑 改进：根据错误类型提供友好提示
            if error.domain == "CommunityError" && error.code == 1 {
                errorMessage = "登录状态已过期，请重新登录"
            } else if error.localizedDescription.contains("网络") {
                errorMessage = "网络连接异常，请检查网络后重试"
            } else if error.localizedDescription.contains("内容") {
                errorMessage = "评论内容不符合要求"
            } else {
                errorMessage = "添加评论失败，请稍后重试"
            }

            #if DEBUG
            print("❌ [Community] 评论添加失败: \(error.localizedDescription)")
            #endif
        }
    }
    
    // MARK: - 私有方法
    
    /// 执行加载帖子操作
    private func performLoadPosts() async {
        guard let container = repositoryContainer else {
            errorMessage = "Repository容器未初始化"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // 通过Repository获取帖子
            let fetchedPosts = try await container.communityRepository.fetchPosts(
                limit: pageSize,
                offset: currentPage * pageSize
            )
                
                if currentPage == 0 {
                posts = fetchedPosts
            } else {
                posts.append(contentsOf: fetchedPosts)
            }
            
            currentPage += 1
            hasNextPage = fetchedPosts.count == pageSize
            
        } catch {
            errorMessage = "加载帖子失败：\(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 执行刷新帖子操作
    private func performRefreshPosts() async {
        guard let container = repositoryContainer else {
            errorMessage = "Repository容器未初始化"
            isRefreshing = false
            return
        }
        
        errorMessage = nil
        
        do {
            // 通过Repository获取最新帖子
            let newPosts = try await container.communityRepository.fetchPosts(
                limit: pageSize,
                offset: 0
            )
            
            posts = newPosts
            currentPage = 1
            hasNextPage = newPosts.count == pageSize
            
        } catch {
            errorMessage = "刷新失败：\(error.localizedDescription)"
        }
        
        isRefreshing = false
    }
    
    /// 执行搜索帖子操作
    private func performSearchPosts(searchText: String) async {
        guard let container = repositoryContainer else {
            errorMessage = "Repository容器未初始化"
            return
        }
        
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            refreshPosts()
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            // 通过Repository搜索帖子
            let searchResults = try await container.communityRepository.searchPosts(
                query: searchText.trimmingCharacters(in: .whitespacesAndNewlines),
                limit: 50
            )
            
            posts = searchResults
            isLoading = false
            
        } catch {
            errorMessage = "搜索失败：\(error.localizedDescription)"
            isLoading = false
        }
    }
    
    // MARK: - 用户认证辅助方法

    /// 🔑 新增：增强版用户获取方法
    private func getEnhancedCurrentUser(container: EARepositoryContainer) async throws -> EAUser? {
        // 第一步：尝试从Repository获取用户
        if let user = try await container.getCurrentUser() {
            return user
        }

        // 第二步：尝试从UserDefaults恢复用户
        if let userIdString = UserDefaults.standard.string(forKey: "currentUserId"),
           let userId = UUID(uuidString: userIdString) {
            if let user = try await container.userRepository.fetchUser(id: userId) {
                #if DEBUG
                print("🔄 [Community] 从UserDefaults恢复用户: \(user.username)")
                #endif
                return user
            }
        }

        // 第三步：尝试获取最近用户
        if let recentUser = try await container.userRepository.fetchCurrentUser() {
            #if DEBUG
            print("🔄 [Community] 使用最近用户: \(recentUser.username)")
            #endif
            return recentUser
        }

        return nil
    }

    // MARK: - 开发辅助方法（移除不存在的方法调用）

    /// 重置社区数据（仅开发阶段使用）
    func resetCommunityData() async {
        // 简单清空本地列表，实际数据由Repository管理
        posts = []
        currentPage = 0
        hasNextPage = true
        errorMessage = nil

        // 重新加载数据
        loadPosts()
    }
}

// MARK: - 错误类型定义

enum CommunityError: LocalizedError {
    case userNotLoggedIn
    case invalidContent
    case networkError
    case dataCorruption
    case dataNotFound
    case insufficientPermissions
    
    var errorDescription: String? {
        switch self {
        case .userNotLoggedIn:
            return "用户未登录"
        case .invalidContent:
            return "内容格式无效"
        case .networkError:
            return "网络连接错误"
        case .dataCorruption:
            return "数据损坏"
        case .dataNotFound:
            return "数据未找到"
        case .insufficientPermissions:
            return "权限不足"
        }
    }
} 
