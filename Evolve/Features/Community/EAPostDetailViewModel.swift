import SwiftUI
import SwiftData

/// 帖子详情页面ViewModel
/// 处理评论加载、点赞交互、评论提交和帖子操作
@MainActor
class EAPostDetailViewModel: ObservableObject {
    
    // MARK: - Properties
    
    /// 帖子数据
    let post: EACommunityPost
    
    /// Repository容器（强制依赖注入）
    private let repositoryContainer: EARepositoryContainer
    
    /// ✅ 修复：添加sessionManager依赖注入（可变，支持运行时设置）
    private var sessionManager: EASessionManager
    
    /// 评论列表
    @Published var comments: [EACommunityComment] = []
    
    /// 评论输入文本
    @Published var commentText: String = ""
    
    /// 回复目标评论
    @Published var replyTargetComment: EACommunityComment?
    
    /// UI状态
    @Published var isLoadingComments: Bool = false
    @Published var isSubmittingComment: Bool = false
    @Published var showAlert: Bool = false
    @Published var alertMessage: String?
    @Published var showDeleteDialog: Bool = false
    
    /// 错误恢复状态
    @Published var loadingError: String?
    @Published var canRetry: Bool = false
    
    /// 评论排序方式
    @Published var commentSortOrder: CommentSortOrder = .newest
    
    /// 当前用户缓存
    private var currentUser: EAUser?
    
    // MARK: - Initialization
    
    /// ✅ 修复：强制Repository模式，移除ModelContext依赖
    init(post: EACommunityPost, sessionManager: EASessionManager, repositoryContainer: EARepositoryContainer) {
        self.post = post
        self.sessionManager = sessionManager
        self.repositoryContainer = repositoryContainer
    }

    
    // MARK: - Public Methods
    
    /// 加载评论列表 - 强制Repository模式
    func loadComments() {
        isLoadingComments = true
        loadCommentsWithRepository(repositoryContainer)
    }
    
    /// 使用Repository加载评论
    private func loadCommentsWithRepository(_ container: EARepositoryContainer) {
        Task { @MainActor in
            do {
                let fetchedComments = try await container.communityRepository.fetchComments(for: post.id)
                self.comments = fetchedComments
                self.isLoadingComments = false
                self.sortComments()
            } catch {
                self.handleLoadingError("加载评论失败：\(error.localizedDescription)")
            }
        }
    }
    

    
    /// 重试加载评论 - 增强重试逻辑
    func retryLoadComments() {
        guard canRetry else { return }
        
        // 🔑 修复：重置错误状态
        loadingError = nil
        canRetry = false
        
        // 🔑 延迟重试，确保ModelContext稳定
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.loadComments()
        }
    }
    
    /// 提交评论
    func submitComment() async {
        guard canSubmitComment else { return }

        // 🔑 修复：设置提交状态，防止重复提交
        await MainActor.run {
            isSubmittingComment = true
        }

        await performCommentSubmission()
    }
    
    /// 执行评论提交的核心逻辑
    private func performCommentSubmission() async {
        do {
            // 🔑 修复：简化评论提交流程，避免重复关系设置
            let currentUser = try await getCurrentUser()

            let trimmedContent = commentText.trimmingCharacters(in: .whitespacesAndNewlines)
            guard !trimmedContent.isEmpty else {
                throw NSError(domain: "CommunityError", code: 2, userInfo: [NSLocalizedDescriptionKey: "评论内容不能为空"])
            }

            // 🔑 修复：直接通过Repository创建评论，避免重复关系设置
            let newComment = try await repositoryContainer.communityRepository.createComment(
                content: trimmedContent,
                authorId: currentUser.id,
                postId: post.id,
                parentCommentId: replyTargetComment?.id
            )

            // 更新UI状态
            await updateUIAfterSubmission(comment: newComment)

        } catch {
            await MainActor.run {
                self.handleSubmissionError(error)
            }
        }
    }
    
    // 🔑 修复：移除不再需要的辅助方法，简化代码结构
    
    /// 更新UI状态
    private func updateUIAfterSubmission(comment: EACommunityComment) async {
        await MainActor.run {
            // 添加到本地列表顶部
            self.comments.insert(comment, at: 0)
            
            // 清空输入框和回复状态
            self.commentText = ""
            self.replyTargetComment = nil
            self.isSubmittingComment = false
        }
    }
    
    /// 处理提交错误
    private func handleSubmissionError(_ error: Error) {
        isSubmittingComment = false
        
        if error is CommunityError {
            showError("评论提交失败：\(error.localizedDescription)")
        } else {
            showError("评论提交失败，请重试")
        }
    }
    
    /// 切换帖子点赞状态
    func togglePostLike() {
        // 此功能委托给EACommunityViewModel处理
        // 这里只是占位，实际集成时需要与主社区ViewModel协调
    }
    
    /// 切换评论点赞状态 - 优化性能
    func toggleCommentLike(_ comment: EACommunityComment) {
        Task { @MainActor in
            await performCommentLikeToggle(comment)
        }
    }
    
    /// 执行评论点赞切换操作 - 拆分复杂逻辑
    private func performCommentLikeToggle(_ comment: EACommunityComment) async {
        do {
            let currentUser = try await getCurrentUser()
            let existingLike = try await findExistingLike(for: comment, user: currentUser)
            
            if let existingLike = existingLike {
                await updateExistingLike(existingLike, for: comment)
            } else {
                await createNewLike(for: comment, user: currentUser)
            }
            
            // Repository模式下，保存操作已在具体方法中完成
            
        } catch {
            showError("点赞操作失败：\(error.localizedDescription)")
        }
    }
    
    /// 查找现有点赞 - 通过Repository
    private func findExistingLike(for comment: EACommunityComment, user: EAUser) async throws -> EACommunityLike? {
        // 通过Repository查找现有点赞
        return try await repositoryContainer.communityRepository.findExistingLike(
            commentId: comment.id,
            userId: user.id
        )
    }
    
    /// 更新现有点赞状态
    private func updateExistingLike(_ existingLike: EACommunityLike, for comment: EACommunityComment) async {
        if existingLike.isActive {
            existingLike.isActive = false
            // 点赞数量通过计算关系获得，无需手动维护计数
        } else {
            existingLike.isActive = true
            // 点赞数量通过计算关系获得，无需手动维护计数
        }
        existingLike.creationDate = Date()
    }
    
    /// 删除现有点赞 - 通过Repository
    private func removeExistingLike(_ existingLike: EACommunityLike, comment: EACommunityComment) async {
        do {
            try await repositoryContainer.communityRepository.deleteLike(existingLike.id)
        } catch {
            await MainActor.run {
                self.showError("删除点赞失败：\(error.localizedDescription)")
            }
        }
    }
    
    /// 重新激活点赞
    private func reactivateLike(_ existingLike: EACommunityLike, comment: EACommunityComment) async {
        existingLike.isActive = true
        // 点赞数量通过计算关系获得，无需手动维护计数
        existingLike.creationDate = Date()
    }
    
    /// 创建新点赞 - 通过Repository
    private func createNewLike(for comment: EACommunityComment, user: EAUser) async {
        do {
            _ = try await repositoryContainer.communityRepository.createLike(
                targetType: "comment",
                targetCommentId: comment.id,
                userId: user.id,
                userEnergyLevel: 5
            )
        } catch {
            await MainActor.run {
                self.showError("创建点赞失败：\(error.localizedDescription)")
            }
        }
    }
    
    /// 准备回复评论
    func prepareReplyToComment(_ comment: EACommunityComment) {
        replyTargetComment = comment
    }
    
    /// 取消回复
    func cancelReply() {
        replyTargetComment = nil
    }
    
    /// 切换评论排序
    func toggleCommentSort() {
        switch commentSortOrder {
        case .newest:
            commentSortOrder = .oldest
        case .oldest:
            commentSortOrder = .mostLiked
        case .mostLiked:
            commentSortOrder = .newest
        }
        sortComments()
    }
    
    /// 分享帖子
    func sharePost() {
        // TODO: 实现分享功能
        showAlert(message: "分享功能开发中...")
    }
    
    /// 举报帖子
    func reportPost() {
        // TODO: 实现举报功能
        showAlert(message: "举报功能开发中...")
    }
    
    /// 显示用户资料
    func showUserProfile(for user: EAUser? = nil) {
        // TODO: 实现用户资料页面
        let username = user?.username ?? post.getAuthorUsername()
        showAlert(message: "查看 \(username) 的个人资料")
    }
    
    /// 显示删除确认
    func showDeleteConfirmation() {
        showDeleteDialog = true
    }
    
    /// 删除帖子 - 通过Repository
    func deletePost() {
        Task {
            do {
                // ✅ 修复：使用getCurrentUser方法确保用户状态正确
                let currentUser = try await getCurrentUser()

                // 检查权限（只有作者可以删除）
                guard self.post.getAuthor()?.id == currentUser.id else {
                    await MainActor.run {
                        self.showError("权限不足")
                    }
                    return
                }

                try await repositoryContainer.communityRepository.deletePost(id: self.post.id)

                await MainActor.run {
                    self.showAlert(message: "帖子删除成功")
                }
            } catch {
                await MainActor.run {
                    self.showError("删除失败：\(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - Computed Properties
    
    /// 是否可以提交评论
    var canSubmitComment: Bool {
        !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isSubmittingComment
    }
    
    /// 是否可以删除帖子
    var canDeletePost: Bool {
        // TODO: 实现权限检查
        true // 临时返回true
    }
    
    /// 是否已点赞帖子
    var isPostLiked: Bool {
        // TODO: 实现帖子点赞状态检查
        false // 临时返回false
    }
    
    /// 评论排序文字
    var commentSortText: String {
        switch commentSortOrder {
        case .newest: return "最新"
        case .oldest: return "最早"
        case .mostLiked: return "最热"
        }
    }
    
    /// 检查评论是否已点赞
    func isCommentLiked(_ comment: EACommunityComment) -> Bool {
        // TODO: 实现评论点赞状态检查
        return false // 临时返回false
    }
    
    // MARK: - Private Methods
    
    /// 处理加载错误 - 新增错误恢复机制
    private func handleLoadingError(_ message: String) {
        isLoadingComments = false
        
        // 🔑 修复：根据错误类型决定是否可重试
        if message.contains("数据上下文未初始化") {
            loadingError = "数据环境正在准备中，请稍后..."
            canRetry = true
        } else {
            loadingError = message
            canRetry = true
        }
        
        showError(message)
    }
    
    /// 排序评论
    private func sortComments() {
        switch commentSortOrder {
        case .newest:
            comments.sort { $0.creationDate > $1.creationDate }
        case .oldest:
            comments.sort { $0.creationDate < $1.creationDate }
        case .mostLiked:
            comments.sort { getCommentLikesCount($0) > getCommentLikesCount($1) }
        }
    }
    
    /// 获取帖子点赞数量 - 通过Repository
    func getPostLikesCount() -> Int {
            // 使用Repository异步获取，这里返回缓存值或0
        return 0 // TODO: 实现缓存机制和异步加载
    }
    
    /// 获取评论点赞数量 - 通过Repository
    func getCommentLikesCount(_ comment: EACommunityComment) -> Int {
            // 使用Repository异步获取，这里返回缓存值或0
        return 0 // TODO: 实现缓存机制和异步加载
    }
    
    /// 显示错误消息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    /// 显示提示消息
    private func showAlert(message: String) {
        alertMessage = message
        showAlert = true
    }
    
    /// 获取当前用户
    private func getCurrentUser() async throws -> EAUser {
        // ✅ 修复：使用依赖注入的sessionManager替代单例
        guard let currentUser = sessionManager.currentUser else {
            // 🔑 新增：尝试从Repository重新获取用户数据
            if let repositoryUser = try await repositoryContainer.getCurrentUser() {
                // 更新sessionManager的用户状态
                await MainActor.run {
                    sessionManager.currentUser = repositoryUser
                    sessionManager.isLoggedIn = true
                }
                return repositoryUser
            }
            throw NSError(domain: "CommunityError", code: 1, userInfo: [NSLocalizedDescriptionKey: "用户未登录"])
        }
        return currentUser
    }
}

// MARK: - CommentSortOrder

/// 评论排序方式
enum CommentSortOrder {
    case newest    // 最新
    case oldest    // 最早
    case mostLiked // 最热
}

// MARK: - Helper Types

/// 评论提交数据结构
private struct CommentSubmissionData {
    let content: String
    let user: EAUser
    let post: EACommunityPost
    let parentComment: EACommunityComment?
}